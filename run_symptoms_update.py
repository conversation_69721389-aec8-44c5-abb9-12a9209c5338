"""
Simple script to run the Excel symptoms updater
"""

from update_excel_symptoms import ExcelSymptomsUpdater
from pathlib import Path
import sys

def main():
    # Input Excel file
    input_file = "diagnosis_data.xlsx"
    
    # Check if file exists
    if not Path(input_file).exists():
        print(f"Error: Excel file '{input_file}' not found!")
        print("Please make sure the file exists in the current directory.")
        return
    
    print(f"Found Excel file: {input_file}")
    
    # Ask user if they want to preview first
    response = input("Do you want to preview the changes first? (y/n): ").lower().strip()
    
    updater = ExcelSymptomsUpdater()
    
    if response in ['y', 'yes']:
        print("\n" + "="*60)
        print("PREVIEW MODE - Showing first 2 rows")
        print("="*60)
        updater.preview_changes(input_file, num_rows=2)
        
        # Ask if they want to proceed
        proceed = input("\nDo you want to proceed with updating the entire file? (y/n): ").lower().strip()
        if proceed not in ['y', 'yes']:
            print("Update cancelled.")
            return
    
    print("\n" + "="*60)
    print("UPDATING EXCEL FILE")
    print("="*60)
    print("This may take a few minutes depending on the number of rows...")
    print("Please wait...")
    
    # Update the file
    success = updater.update_excel_file(input_file)
    
    if success:
        print("\n" + "="*60)
        print("SUCCESS!")
        print("="*60)
        print(f"✓ Original file: {input_file}")
        print(f"✓ Updated file: diagnosis_data_updated.xlsx")
        print("✓ The 'symptoms' column has been renamed to 'old_symptom'")
        print("✓ A new 'symptoms' column has been added with improved text")
    else:
        print("\n" + "="*60)
        print("FAILED!")
        print("="*60)
        print("The update process failed. Please check the logs for details.")

if __name__ == "__main__":
    main()
