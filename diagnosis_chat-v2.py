#!/usr/bin/env python3
"""
Terminal-based Diagnosis Chat System
Acts as a medical diagnosis expert using vector search with Ollama
Asks follow-up questions (max 5) and provides diagnosis with confidence scores
"""

import os
import sys
import psycopg2
import requests
import openai
from typing import List, Dict, <PERSON><PERSON>
from dotenv import load_dotenv
from pathlib import Path
import json
import uuid
from datetime import datetime
import random
import re

# Load environment variables
load_dotenv()

class OpenAIEmbedder:
    """Embedder using OpenAI API for text embeddings"""

    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
        self.model_name = model or os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")

        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a list of texts using OpenAI API"""
        embeddings = []

        try:
            response = requests.post(
                f"{self.base_url}/embeddings",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model_name,
                    "input": texts
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                data = result.get("data", [])

                for item in data:
                    embedding = item.get("embedding", [])
                    if embedding:
                        embeddings.append(embedding)
            # No fallback: do not generate random embeddings
        except Exception:
            pass  # No fallback

        return embeddings

class DiagnosisDatabaseV2:
    """Database manager for diagnosis vector search"""

    def __init__(self):
        self.database_url = os.getenv("DATABASE_URL")
        
        if self.database_url:
            self.db_config = None
        else:
            self.db_config = {
                "dbname": os.getenv("DB_NAME", "kforce"),
                "user": os.getenv("DB_USER", "postgres"),
                "password": os.getenv("DB_PASS", "kforce"),
                "host": os.getenv("DB_HOST", "localhost"),
                "port": os.getenv("DB_PORT", "5444")
            }

    def get_connection(self):
        """Get database connection"""
        if self.database_url:
            return psycopg2.connect(self.database_url)
        else:
            return psycopg2.connect(**self.db_config)

    def search_similar_diagnoses(self, query_embedding: List[float], top_k: int = 5) -> List[Dict]:
        """Search for similar diagnoses using vector similarity"""
        
        search_sql = """
        SELECT
            rd.name,
            d.symptoms,
            CASE
                WHEN symptoms_embedding IS NULL THEN 0.0
                WHEN %s::vector IS NULL THEN 0.0
                ELSE GREATEST(0.0, LEAST(1.0, 1 - (symptoms_embedding <=> %s::vector)))
            END as confidence_score,
            diagnosis_id
        FROM config.diagnoses_embeddings as d
        LEFT JOIN main.ref_diagnosis as rd ON d.diagnosis_id = rd.reference_id
        WHERE d.symptoms_embedding IS NOT NULL
        ORDER BY d.symptoms_embedding <=> %s::vector
        LIMIT %s
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    print("Running SQL query:")
                    print(cur.mogrify(search_sql, (query_embedding, query_embedding, query_embedding, top_k)).decode())
                    cur.execute(search_sql, (query_embedding, query_embedding, query_embedding, top_k))
                    
                    results = cur.fetchall()

                    diagnoses = []
                    for row in results:
                        diagnoses.append({
                            "name": row[0],
                            "symptoms": row[1],
                            "confidence_score": float(row[2]),
                            "diagnosis_id": row[3]
                        })

                    return diagnoses

        except Exception:
            return []

    def get_diagnosis_count(self) -> int:
        """Get total number of diagnoses in database"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT COUNT(*) FROM diagnoses")
                    return cur.fetchone()[0]
        except Exception:
            return 0

class GroqLLM:
    """LLM interface using Groq API"""

    def __init__(self):
        self.api_key = os.getenv("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"
        self.model = "llama-3.3-70b-versatile"

    def _extract_symptoms_summary(self, conversation_history: List[Dict]) -> str:
        if not conversation_history:
            return "No symptoms provided"

        initial_symptoms = ""
        additional_info = []

        for step in conversation_history:
            if isinstance(step, dict):
                if step.get('role') == 'user':
                    content = step.get('content', '')
                    if not initial_symptoms and content:
                        initial_symptoms = content
                    elif content and not content.lower().startswith('no'):
                        additional_info.append(content)
                elif step.get('type') == 'initial_symptoms':
                    initial_symptoms = step.get('content', '')
                elif step.get('type') == 'answer':
                    answer = step.get('content', '').strip()
                    if answer and not answer.lower().startswith('no'):
                        additional_info.append(answer)

        if additional_info:
            return f"{initial_symptoms}. Additional findings: {'; '.join(additional_info)}"
        else:
            return initial_symptoms

    def generate_follow_up_question(self, symptoms_so_far: str, top_diagnoses: List[Dict], previous_questions: List[str], conversation_history: List[Dict] = None) -> str:
        diagnosis_context = "\n".join([
            f"- {d['name']} (confidence: {d['confidence_score']:.2f}): {d['symptoms'][:200]}..."
            for d in top_diagnoses[:3]
        ])

        conversation_json = "[]"
        if conversation_history:
            try:
                chat_format = []
                for step in conversation_history:
                    if step.get('role'):
                        chat_format.append(step)
                    elif step.get('type') == 'initial_symptoms':
                        chat_format.append({
                            "role": "user",
                            "content": step.get('content', ''),
                            "id": str(uuid.uuid4()),
                            "createdAt": datetime.now().isoformat() + "Z",
                            "parts": [
                                {
                                    "type": "text",
                                    "text": step.get('content', '')
                                }
                            ]
                        })
                    elif step.get('type') == 'question':
                        chat_format.append({
                            "role": "assistant",
                            "content": step.get('content', ''),
                            "id": str(uuid.uuid4()),
                            "createdAt": datetime.now().isoformat() + "Z",
                            "parts": [
                                {
                                    "type": "text",
                                    "text": step.get('content', '')
                                }
                            ]
                        })
                    elif step.get('type') == 'answer':
                        chat_format.append({
                            "role": "user",
                            "content": step.get('content', ''),
                            "id": str(uuid.uuid4()),
                            "createdAt": datetime.now().isoformat() + "Z",
                            "parts": [
                                {
                                    "type": "text",
                                    "text": step.get('content', '')
                                }
                            ]
                        })

                conversation_json = json.dumps(chat_format, indent=2)
            except Exception:
                conversation_json = "[]"

        excluded_context = ""
        if hasattr(self, 'excluded_diagnoses') and self.excluded_diagnoses:
            excluded_context = f"\nExcluded diagnoses (don't ask about these): {', '.join(self.excluded_diagnoses)}"

        current_symptoms_summary = self._extract_symptoms_summary(conversation_history or [])

        prompt = f"""You are a medical diagnosis expert. Based on the symptoms provided and potential diagnoses, ask ONE specific follow-up question to help narrow down the diagnosis.

Current symptoms summary: {current_symptoms_summary}

Conversation history (JSON format):
{conversation_json}

Top potential diagnoses:
{diagnosis_context}{excluded_context}

IMPORTANT:
- Ask only questions based on the symptoms given . do not give questions out of this
- Ask a specific, targeted question that would help differentiate between these diagnoses
- Do NOT repeat or rephrase any previous questions from the conversation history
- Do NOT ask about excluded diagnoses or their symptoms
- If the diagnoses are too similar or no good differentiating question exists, respond with "NO_QUESTION"
- Keep it concise and medical professional
- Only ask ONE question
"""

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model,
                    "messages": [{"role": "user", "content": prompt}],
                    "max_tokens": 150,
                    "temperature": 0.7
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                question = result["choices"][0]["message"]["content"].strip()

                if "NO_QUESTION" in question.upper():
                    return None

                return question
            else:
                return None

        except Exception:
            return None

class OpenAILLM:
    """LLM interface using OpenAI API"""

    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.model = "gpt-5-mini-2025-08-07"
        openai.api_key = self.api_key

    def _extract_symptoms_summary(self, conversation_history: List[Dict]) -> str:
        if not conversation_history:
            return "No symptoms provided"

        initial_symptoms = ""
        additional_info = []

        for step in conversation_history:
            if isinstance(step, dict):
                if step.get('role') == 'user':
                    content = step.get('content', '')
                    if not initial_symptoms and content:
                        initial_symptoms = content
                    elif content and not content.lower().startswith('no'):
                        additional_info.append(content)
                elif step.get('type') == 'initial_symptoms':
                    initial_symptoms = step.get('content', '')
                elif step.get('type') == 'answer':
                    answer = step.get('content', '').strip()
                    if answer and not answer.lower().startswith('no'):
                        additional_info.append(answer)

        if additional_info:
            return f"{initial_symptoms}. Additional findings: {'; '.join(additional_info)}"
        else:
            return initial_symptoms

    def generate_follow_up_question(self, symptoms_so_far: str, top_diagnoses: List[Dict], previous_questions: List[str], conversation_history: List[Dict] = None) -> str:
        diagnosis_context = "\n".join([
            f"- {d['name']} (confidence: {d['confidence_score']:.2f}): {d['symptoms'][:200]}..."
            for d in top_diagnoses[:3]
        ])

        conversation_json = "[]"
        if conversation_history:
            try:
                chat_format = []
                for step in conversation_history:
                    if step.get('role'):
                        chat_format.append(step)
                    elif step.get('type') == 'initial_symptoms':
                        chat_format.append({
                            "role": "user",
                            "content": step.get('content', ''),
                            "id": str(uuid.uuid4()),
                            "createdAt": datetime.now().isoformat() + "Z",
                            "parts": [
                                {
                                    "type": "text",
                                    "text": step.get('content', '')
                                }
                            ]
                        })
                    elif step.get('type') == 'question':
                        chat_format.append({
                            "role": "assistant",
                            "content": step.get('content', ''),
                            "id": str(uuid.uuid4()),
                            "createdAt": datetime.now().isoformat() + "Z",
                            "parts": [
                                {
                                    "type": "step-start"
                                },
                                {
                                    "type": "text",
                                    "text": step.get('content', '')
                                }
                            ]
                        })
                    elif step.get('type') == 'answer':
                        chat_format.append({
                            "role": "user",
                            "content": step.get('content', ''),
                            "id": str(uuid.uuid4()),
                            "createdAt": datetime.now().isoformat() + "Z",
                            "parts": [
                                {
                                    "type": "text",
                                    "text": step.get('content', '')
                                }
                            ]
                        })

                conversation_json = json.dumps(chat_format, indent=2)
            except Exception:
                conversation_json = "[]"

        excluded_context = ""
        if hasattr(self, 'excluded_diagnoses') and self.excluded_diagnoses:
            excluded_context = f"\nExcluded diagnoses (don't ask about these): {', '.join(self.excluded_diagnoses)}"

        current_symptoms_summary = self._extract_symptoms_summary(conversation_history or [])

        prompt = f"""You are a medical diagnosis expert. Based on the symptoms provided and potential diagnoses, ask ONE specific follow-up question to help narrow down the diagnosis.

Current symptoms summary: {current_symptoms_summary}

Conversation history (JSON format):
{conversation_json}

Top potential diagnoses:
{diagnosis_context}{excluded_context}

IMPORTANT:
- Ask a specific, targeted question that would help differentiate between these diagnoses
- Do NOT repeat or rephrase any previous questions from the conversation history
- Do NOT ask about excluded diagnoses or their symptoms
- If the diagnoses are too similar or no good differentiating question exists, respond with "NO_QUESTION"
- Keep it concise and medical professional
- Only ask ONE question"""
        print("prompt",prompt)
        try:
            if not self.api_key:
                print("ERROR: No OpenAI API key available")
                return None

            response = openai.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=150,
                temperature=0.7
            )
            question = response.choices[0].message.content.strip()
            print(f"LLM Response: {question}")
            if "NO_QUESTION" in question.upper():
                print("LLM returned NO_QUESTION")
                return None
            return question
        except Exception as e:
            print(f"ERROR in generate_follow_up_question: {e}")
            return None

class DiagnosisChat:
    """Main diagnosis chat system"""

    def __init__(self):
        self.embedder = OpenAIEmbedder()
        self.database = DiagnosisDatabaseV2()
        self.llm = OpenAILLM()
        self.max_questions = 5
        self.question_count = 0
        self.symptoms_history = []
        self.previous_questions = []
        self.conversation_history = []
        # Cache for embeddings to avoid regenerating
        self.cached_embedding = None
        self.cached_diagnoses = None

    def start_chat(self):
        """Start the diagnosis chat session"""
        print("🏥 Medical Diagnosis Expert Chat")
        print("=" * 50)
        
        # Check database
        diagnosis_count = self.database.get_diagnosis_count()
        if diagnosis_count == 0:
            print("❌ No diagnoses found in database. Please run the embedding system first.")
            return

        print(f"📊 Database contains {diagnosis_count} diagnoses")
        print("\n🩺 I'm here to help with medical diagnosis based on your symptoms.")
        print("Please describe your symptoms, and I'll ask follow-up questions to narrow down the diagnosis.")
        print("Type 'quit' to exit.\n")

        # Get initial symptoms
        initial_symptoms = input("👤 Please describe your main symptoms: ").strip()
        
        if initial_symptoms.lower() in ['quit', 'exit', 'q']:
            print("👋 Goodbye!")
            return
        
        self.symptoms_history.append(initial_symptoms)
        self.question_count = 0
        self.previous_questions = []
        self.conversation_history = [{"step": 1, "type": "initial_symptoms", "content": initial_symptoms}]

        # Generate embedding and fetch diagnoses only once
        self.generate_initial_embedding_and_diagnoses()
        
        # Start diagnosis process
        self.process_symptoms()

    def generate_initial_embedding_and_diagnoses(self):
        """Generate embedding and fetch diagnoses only once"""
        if not self.symptoms_history:
            return
            
        initial_symptoms = self.symptoms_history[0]  # Use only the first (main) symptoms
        print(f"🔄 Generating embedding for: {initial_symptoms}")
        
        embeddings = self.embedder.get_embeddings([initial_symptoms])
        if embeddings:
            self.cached_embedding = embeddings[0]
            print("✅ Embedding cached successfully")
            
            # Now fetch and cache diagnoses - ONLY ONCE
            print("🔄 Fetching diagnoses from database...")
            diagnoses = self.database.search_similar_diagnoses(self.cached_embedding, top_k=10)
            
            # Filter by confidence threshold and cache
            self.cached_diagnoses = [d for d in diagnoses if d['confidence_score'] >= 0.3]
            print(f"✅ {len(self.cached_diagnoses)} diagnoses cached successfully")
        else:
            print("❌ Error generating embedding")

    def get_cached_diagnoses(self):
        """Return cached diagnoses without any database query"""
        if self.cached_diagnoses is None:
            print("❌ No cached diagnoses available")
            return []
        
        # Return a copy to avoid modifying the original cached data
        return self.cached_diagnoses.copy()

    def process_symptoms(self):
        """Process current symptoms and provide diagnosis or ask follow-up"""
        
        # Use cached diagnoses - NO database queries after initial load
        if self.cached_diagnoses is None:
            print("❌ No cached diagnoses available. Please restart the session.")
            return
            
        # Get cached diagnoses (no DB query)
        diagnoses = self.get_cached_diagnoses()
        
        if not diagnoses:
            print("❌ No matching diagnoses found. Please consult a medical professional.")
            return

        # Apply intelligent filtering based on conversation context
        filtered_diagnoses = self.filter_diagnoses_by_context(diagnoses)

        # Display current top diagnoses
        print(f"\n🔍 Current Analysis (Question {self.question_count + 1}):")
        print(f"📊 Using cached results (no database query)")
        print("-" * 40)
        
        for i, diagnosis in enumerate(filtered_diagnoses[:5], 1):
            confidence_percent = diagnosis['confidence_score'] * 100
            print(f"{i}. {diagnosis['name']} (#{diagnosis['diagnosis_id']})")
            print(f"   Confidence: {confidence_percent:.1f}%")
            print(f"   Key symptoms: {diagnosis['symptoms'][:100]}...")

        # Check if we should ask more questions
        top_confidence = filtered_diagnoses[0]['confidence_score'] if filtered_diagnoses else 0

        if self.question_count >= self.max_questions or top_confidence > 0.8:
            # Final diagnosis
            self.provide_final_diagnosis(filtered_diagnoses[0])
        else:
            # Try to ask follow-up question
            success = self.ask_follow_up_question(filtered_diagnoses)
            if not success:
                # No good question found, provide final diagnosis
                print("\n🎯 No further differentiating questions available.")
                self.provide_final_diagnosis(filtered_diagnoses[0])

    def filter_diagnoses_by_context(self, diagnoses):
        """Filter diagnoses based on conversation context without regenerating embeddings"""
        
        if not self.conversation_history or len(self.conversation_history) <= 1:
            return diagnoses  # No filtering for initial symptoms
        
        # Create a simple scoring system based on follow-up answers
        scored_diagnoses = []
        
        for diagnosis in diagnoses:
            base_score = diagnosis['confidence_score']
            context_bonus = 0
            context_penalty = 0
            
            # Analyze each Q&A pair for relevance to this diagnosis
            for i in range(len(self.conversation_history)):
                entry = self.conversation_history[i]
                
                if entry.get('type') == 'answer':
                    answer = entry.get('content', '').lower()
                    
                    # Get the corresponding question
                    question = ""
                    if i > 0 and self.conversation_history[i-1].get('type') == 'question':
                        question = self.conversation_history[i-1].get('content', '').lower()
                    
                    # Simple keyword matching between answer and diagnosis symptoms
                    diagnosis_symptoms = diagnosis['symptoms'].lower()
                    
                    # Positive indicators
                    if any(word in answer for word in ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']):
                        # Check if the question keywords match diagnosis symptoms
                        question_keywords = self.extract_keywords_from_question(question)
                        if any(keyword in diagnosis_symptoms for keyword in question_keywords):
                            context_bonus += 0.1
                    
                    # Negative indicators
                    elif any(word in answer for word in ['no', 'not', 'never', 'absent', 'negative']):
                        question_keywords = self.extract_keywords_from_question(question)
                        if any(keyword in diagnosis_symptoms for keyword in question_keywords):
                            context_penalty += 0.15
            
            # Calculate adjusted score
            adjusted_score = max(0, base_score + context_bonus - context_penalty)
            
            scored_diagnoses.append({
                **diagnosis,
                'confidence_score': adjusted_score,
                'original_score': base_score,
                'context_adjustment': context_bonus - context_penalty
            })
        
        # Sort by adjusted confidence score
        scored_diagnoses.sort(key=lambda x: x['confidence_score'], reverse=True)
        
        # Filter out diagnoses with very low adjusted scores
        filtered = [d for d in scored_diagnoses if d['confidence_score'] >= 0.2]
        
        return filtered

    def extract_keywords_from_question(self, question):
        """Extract relevant medical keywords from a question"""
        import re
        
        # Common medical terms and symptoms
        medical_keywords = []
        
        # Remove common question words
        question_clean = re.sub(r'\b(do|you|have|any|are|there|is|was|were|been|being|experience|experiencing|feel|feeling)\b', '', question, flags=re.IGNORECASE)
        
        # Extract potential symptom words (longer than 3 characters)
        words = re.findall(r'\b[a-zA-Z]{4,}\b', question_clean)
        
        # Filter out common non-medical words
        common_words = {'with', 'that', 'this', 'they', 'them', 'when', 'where', 'what', 'which', 'would', 'could', 'should', 'might', 'such', 'like', 'also', 'even', 'just', 'only', 'more', 'most', 'much', 'many', 'some', 'time', 'times', 'often', 'usually', 'sometimes', 'always', 'never'}
        
        medical_keywords = [word.lower() for word in words if word.lower() not in common_words]
        
        return medical_keywords

    def ask_follow_up_question(self, diagnoses: List[Dict]) -> bool:
        """Ask a follow-up question to narrow down diagnosis. Returns True if successful, False if no question available."""

        self.question_count += 1

        # Generate follow-up question using current symptoms summary
        combined_symptoms = " ".join(self.symptoms_history)
        question = self.llm.generate_follow_up_question(combined_symptoms, diagnoses, self.previous_questions, self.conversation_history)

        if question is None:
            # No good question available
            return False

        print(f"🩺 Follow-up Question {self.question_count}/{self.max_questions}:")
        print(f"   {question}")

        # Store the question to avoid repetition
        self.previous_questions.append(question)

        # Add question to conversation history
        self.conversation_history.append({
            "step": len(self.conversation_history) + 1,
            "type": "question",
            "content": question,
            "question_number": self.question_count
        })

        # Get user response
        response = input("\n👤 Your answer: ").strip()

        if response.lower() in ['quit', 'exit', 'q']:
            print("👋 Goodbye!")
            return True

        if response:
            self.symptoms_history.append(response)

            # Add answer to conversation history
            self.conversation_history.append({
                "step": len(self.conversation_history) + 1,
                "type": "answer",
                "content": response,
                "question_number": self.question_count
            })

            # Process symptoms again (but without regenerating embeddings)
            self.process_symptoms()
            return True
        else:
            print("Please provide an answer to continue.")
            return self.ask_follow_up_question(diagnoses)

    def provide_final_diagnosis(self, top_diagnosis: Dict):
        """Provide final diagnosis with recommendations"""
        
        confidence_percent = top_diagnosis['confidence_score'] * 100
        
        print("\n🎯 FINAL DIAGNOSIS:")
        print("=" * 50)
        print(f"Diagnosis: {top_diagnosis['name']}")
        print(f"Confidence: {confidence_percent:.1f}%")
        
        # Show adjustment info if available
        if 'original_score' in top_diagnosis:
            original_percent = top_diagnosis['original_score'] * 100
            print(f"Original confidence: {original_percent:.1f}%")
        
        print(f"\nSymptoms: {top_diagnosis['symptoms']}")
        
        print("\n⚠️  IMPORTANT DISCLAIMER:")
        print("This is an AI-based analysis for informational purposes only.")
        print("Please consult with a qualified medical professional for proper diagnosis and treatment.")
        
        # Ask if user wants to start over
        print("\n" + "=" * 50)
        restart = input("Would you like to start a new diagnosis session? (y/n): ").strip().lower()
        
        if restart in ['y', 'yes']:
            # Reset all cached data for new session
            self.symptoms_history = []
            self.question_count = 0
            self.previous_questions = []
            self.conversation_history = []
            self.cached_embedding = None
            self.cached_diagnoses = None
            print("\n" + "=" * 50)
            self.start_chat()
        else:
            print("👋 Thank you for using the Medical Diagnosis Expert Chat!")

    # Keep the other methods unchanged
    def create_enhanced_symptoms_summary(self) -> str:
        """Create an enhanced symptoms summary that includes inferred symptoms from positive answers"""
        if not self.conversation_history:
            return " ".join(self.symptoms_history)

        # Start with initial symptoms
        initial_symptoms = ""
        enhanced_symptoms = []

        for entry in self.conversation_history:
            if entry.get('type') == 'initial_symptoms':
                initial_symptoms = entry.get('content', '')
                enhanced_symptoms.append(initial_symptoms)
            elif entry.get('type') == 'question':
                # Store the question for the next answer
                current_question = entry.get('content', '')
            elif entry.get('type') == 'answer':
                answer = entry.get('content', '').strip().lower()

                # If user gave a positive answer, extract symptoms from the previous question
                if any(positive in answer for positive in ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']):
                    if 'current_question' in locals():
                        extracted_symptoms = self.extract_symptoms_from_question(current_question)
                        enhanced_symptoms.extend(extracted_symptoms)
                # If user gave additional descriptive information (not just yes/no)
                elif answer not in ['yes', 'no', 'none', 'never', 'not', 'negative', 'absent']:
                    enhanced_symptoms.append(answer)

        # Remove duplicates while preserving order
        seen = set()
        unique_symptoms = []
        for symptom in enhanced_symptoms:
            if symptom.lower() not in seen:
                seen.add(symptom.lower())
                unique_symptoms.append(symptom)

        return " ".join(unique_symptoms)

    def extract_symptoms_from_question(self, question: str) -> List[str]:
        """Extract specific symptoms mentioned in a follow-up question"""
        import re

        question_lower = question.lower()
        symptoms = []

        # Common patterns for symptoms in questions
        symptom_patterns = [
            r'such as ([^?]+)',  # "such as cough or difficulty breathing"
            r'including ([^?]+)',  # "including fever or chills"
            r'like ([^?]+)',  # "like vomiting or diarrhea"
            r'any ([^,?]+)',  # "any pain or discomfort"
        ]

        for pattern in symptom_patterns:
            matches = re.findall(pattern, question_lower)
            for match in matches:
                # Split by 'or', 'and', commas and clean up
                symptom_parts = re.split(r'\s+or\s+|\s+and\s+|,', match)
                for part in symptom_parts:
                    clean_symptom = part.strip()
                    if clean_symptom and len(clean_symptom) > 2:
                        symptoms.append(clean_symptom)
        return symptoms

def main():
    """Main entry point"""
    try:
        chat = DiagnosisChat()
        chat.start_chat()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        print("Please check your database connection and try again.")

if __name__ == "__main__":
    main()
