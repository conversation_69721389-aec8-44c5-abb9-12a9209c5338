"""
Excel Symptoms Updater
Updates the symptoms column in the Excel file by:
1. Renaming 'symptoms' column to 'old_symptom'
2. Using OpenAI to improve symptom descriptions
3. Adding a new 'symptoms' column with improved text
"""

import os
import pandas as pd
import requests
import logging
from typing import List
from dotenv import load_dotenv
from pathlib import Path
import time

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('excel_symptoms_updater.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OpenAITextImprover:
    """Use OpenAI to improve symptom text descriptions"""

    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
        self.model_name = model or os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")

        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

        logger.info(f"Initialized OpenAITextImprover with model: {self.model_name}")

    def improve_symptom_text(self, symptom_text: str) -> str:
        """Improve a single symptom text using OpenAI"""
        prompt = f"I'm giving you data. put it into proper sentence:\n\n{symptom_text}"
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": self.model_name,
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": 500,
                    "temperature": 0.3
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                improved_text = result['choices'][0]['message']['content'].strip()
                logger.info(f"Successfully improved symptom text")
                return improved_text
            else:
                logger.error(f"Error from OpenAI API: {response.status_code} - {response.text}")
                return symptom_text  # Return original text if API fails

        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            return symptom_text  # Return original text if API fails

    def improve_symptoms_batch(self, symptom_texts: List[str], delay: float = 1.0) -> List[str]:
        """Improve multiple symptom texts with delay between requests"""
        improved_texts = []
        
        for i, symptom_text in enumerate(symptom_texts):
            logger.info(f"Processing symptom {i+1}/{len(symptom_texts)}")
            improved_text = self.improve_symptom_text(symptom_text)
            improved_texts.append(improved_text)
            
            # Add delay to avoid rate limiting
            if i < len(symptom_texts) - 1:  # Don't delay after the last request
                time.sleep(delay)
        
        return improved_texts

class ExcelSymptomsUpdater:
    """Main class for updating Excel symptoms"""

    def __init__(self):
        self.text_improver = OpenAITextImprover()
        logger.info("Initialized ExcelSymptomsUpdater")

    def update_excel_file(self, input_excel_path: str, output_excel_path: str = None):
        """Update the Excel file with improved symptoms"""
        logger.info(f"Updating Excel file: {input_excel_path}")
        
        if not Path(input_excel_path).exists():
            logger.error(f"Excel file {input_excel_path} does not exist")
            return False

        try:
            # Read the Excel file
            df = pd.read_excel(input_excel_path)
            logger.info(f"Read Excel file with {len(df)} rows")
            
            # Check if symptoms column exists
            if 'symptoms' not in df.columns:
                logger.error("'symptoms' column not found in Excel file")
                return False

            # Rename symptoms column to old_symptom
            df = df.rename(columns={'symptoms': 'old_symptom'})
            logger.info("Renamed 'symptoms' column to 'old_symptom'")

            # Get the old symptom texts
            old_symptoms = df['old_symptom'].astype(str).tolist()
            
            # Improve the symptom texts using OpenAI
            logger.info("Starting to improve symptom texts using OpenAI...")
            improved_symptoms = self.text_improver.improve_symptoms_batch(old_symptoms)
            
            # Add the improved symptoms as a new column
            df['symptoms'] = improved_symptoms
            logger.info("Added improved symptoms as new 'symptoms' column")

            # Reorder columns to have symptoms after old_symptom
            columns = df.columns.tolist()
            if 'symptoms' in columns:
                columns.remove('symptoms')
                old_symptom_index = columns.index('old_symptom')
                columns.insert(old_symptom_index + 1, 'symptoms')
                df = df[columns]

            # Save the updated Excel file
            if output_excel_path is None:
                # Create output filename by adding '_updated' before the extension
                input_path = Path(input_excel_path)
                output_excel_path = input_path.parent / f"{input_path.stem}_updated{input_path.suffix}"
            
            df.to_excel(output_excel_path, index=False)
            logger.info(f"Saved updated Excel file to: {output_excel_path}")
            
            # Print summary
            print(f"\nUpdate Summary:")
            print(f"- Input file: {input_excel_path}")
            print(f"- Output file: {output_excel_path}")
            print(f"- Total rows processed: {len(df)}")
            print(f"- Columns: {list(df.columns)}")
            
            return True

        except Exception as e:
            logger.error(f"Error updating Excel file: {e}")
            return False

    def preview_changes(self, input_excel_path: str, num_rows: int = 3):
        """Preview the changes that would be made without saving"""
        logger.info(f"Previewing changes for: {input_excel_path}")
        
        if not Path(input_excel_path).exists():
            logger.error(f"Excel file {input_excel_path} does not exist")
            return

        try:
            # Read the Excel file
            df = pd.read_excel(input_excel_path)
            
            # Take only the first few rows for preview
            preview_df = df.head(num_rows).copy()
            
            # Get the old symptom texts
            old_symptoms = preview_df['symptoms'].astype(str).tolist()
            
            # Improve the symptom texts using OpenAI
            logger.info(f"Generating preview for {len(old_symptoms)} rows...")
            improved_symptoms = self.text_improver.improve_symptoms_batch(old_symptoms)
            
            # Show the comparison
            print(f"\nPreview of changes (first {num_rows} rows):")
            print("=" * 80)
            
            for i, (old, new) in enumerate(zip(old_symptoms, improved_symptoms)):
                print(f"\nRow {i+1}:")
                print(f"Original: {old[:200]}{'...' if len(old) > 200 else ''}")
                print(f"Improved: {new[:200]}{'...' if len(new) > 200 else ''}")
                print("-" * 40)

        except Exception as e:
            logger.error(f"Error previewing changes: {e}")


if __name__ == "__main__":
    import sys
    
    # Default input file
    input_file = "diagnosis_data.xlsx"
    
    # Check if custom input file is provided
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    
    # Check if preview mode is requested
    preview_mode = "--preview" in sys.argv
    
    if not Path(input_file).exists():
        print(f"Excel file '{input_file}' not found!")
        sys.exit(1)
    
    updater = ExcelSymptomsUpdater()
    
    if preview_mode:
        print("Running in preview mode...")
        updater.preview_changes(input_file)
    else:
        print("Updating Excel file...")
        success = updater.update_excel_file(input_file)
        if success:
            print("Excel file updated successfully!")
        else:
            print("Failed to update Excel file!")
            sys.exit(1)
